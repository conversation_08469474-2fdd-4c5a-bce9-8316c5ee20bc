import cv2
import numpy as np
from PIL import Image
import io
import base64

class ChessImagePreprocessor:
    def __init__(self):
        self.min_content_ratio = 0.7  # Minimum content area ratio to consider complete
    
    def preprocess_image(self, image_bytes):
        """Main preprocessing pipeline"""
        # Convert to OpenCV format
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        results = {
            'processed_image': None,
            'warnings': [],
            'preprocessing_notes': []
        }
        
        # Content completeness check removed
        
        # 2. Detect and reduce glare
        img_deglared = self._reduce_glare(img)
        if img_deglared is not None:
            img = img_deglared
            results['preprocessing_notes'].append("Glare reduction applied")
        
        # 3. Geometric corrections
        img_corrected = self._apply_geometric_corrections(img)
        if img_corrected is not None:
            img = img_corrected
            results['preprocessing_notes'].append("Geometric corrections applied")
        
        # 5. Enhance contrast for better OCR
        img_enhanced = self._enhance_for_ocr(img)
        results['preprocessing_notes'].append("Contrast enhancement applied")
        
        # Convert back to bytes
        _, buffer = cv2.imencode('.jpg', img_enhanced)
        results['processed_image'] = buffer.tobytes()
        
        return results
    

    
    def _reduce_glare(self, img):
        """Detect and reduce glare/highlights"""
        # Convert to LAB color space
        lab = cv2.cvtColor(img, cv2.COLOR_BGR2LAB)
        l, a, b = cv2.split(lab)
        
        # Detect bright areas (potential glare)
        bright_threshold = np.percentile(l, 95)
        glare_mask = l > bright_threshold
        
        # If significant glare detected, apply correction
        if np.sum(glare_mask) > (img.shape[0] * img.shape[1] * 0.05):  # 5% of image
            # Reduce brightness in glare areas
            l_corrected = l.copy()
            l_corrected[glare_mask] = np.clip(l_corrected[glare_mask] * 0.8, 0, 255)
            
            # Merge back
            lab_corrected = cv2.merge([l_corrected, a, b])
            img_corrected = cv2.cvtColor(lab_corrected, cv2.COLOR_LAB2BGR)
            return img_corrected
        
        return None
    
    def _apply_geometric_corrections(self, img):
        """Apply perspective and rotation corrections"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Try to detect document edges for perspective correction
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Look for rectangular contours (potential page boundaries)
        for contour in sorted(contours, key=cv2.contourArea, reverse=True)[:5]:
            # Approximate contour
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)
            
            # If we found a quadrilateral that's large enough
            if len(approx) == 4 and cv2.contourArea(approx) > (img.shape[0] * img.shape[1] * 0.3):
                # Apply perspective correction
                return self._correct_perspective(img, approx)
        
        # Skip rotation correction to avoid cutting off content
        return img
    
    def _correct_perspective(self, img, corners):
        """Correct perspective distortion without mirroring"""
        # Skip perspective correction to avoid mirroring issues
        # Return original image
        return img
    
    def _correct_rotation(self, img):
        """Correct rotation using text line detection"""
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Detect lines using HoughLines
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is not None:
            angles = []
            for line in lines[:10]:  # Use top 10 lines
                rho, theta = line[0]
                angle = theta * 180 / np.pi
                # Convert to rotation angle
                if angle > 90:
                    angle = angle - 180
                angles.append(angle)
            
            # Get median angle
            if angles:
                rotation_angle = np.median(angles)
                
                # Only rotate if angle is significant
                if abs(rotation_angle) > 1:
                    center = (img.shape[1]//2, img.shape[0]//2)
                    rotation_matrix = cv2.getRotationMatrix2D(center, rotation_angle, 1.0)
                    rotated = cv2.warpAffine(img, rotation_matrix, (img.shape[1], img.shape[0]))
                    return rotated
        
        return img
    
    def _enhance_for_ocr(self, img):
        """Enhance image for better OCR accuracy"""
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Apply slight Gaussian blur to reduce noise
        enhanced = cv2.GaussianBlur(enhanced, (3, 3), 0)
        
        # Convert back to BGR for consistency
        enhanced_bgr = cv2.cvtColor(enhanced, cv2.COLOR_GRAY2BGR)
        
        return enhanced_bgr
    

    
