import cv2
import numpy as np
from typing import Tuple, List

class ChessScoreSheetRotationDetector:
    def __init__(self):
        self.min_line_length = 50
        self.max_line_gap = 10
        
    def detect_rotation(self, image_bytes: bytes) -> Tuple[bool, float]:
        """
        Detect if chess scoresheet is rotated
        Returns: (needs_rotation, suggested_angle)
        """
        # Convert bytes to cv2 image
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Detect edges
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # Detect lines using HoughLinesP
        lines = cv2.HoughLinesP(
            edges, 
            1, 
            np.pi/180, 
            threshold=100,
            minLineLength=self.min_line_length,
            maxLineGap=self.max_line_gap
        )
        
        if lines is None:
            return False, 0
        
        # Calculate angles of all lines
        angles = []
        for line in lines:
            x1, y1, x2, y2 = line[0]
            angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
            angles.append(angle)
        
        # Classify lines as horizontal or vertical
        horizontal_count = 0
        vertical_count = 0
        
        for angle in angles:
            # Normalize angle to [-90, 90]
            normalized_angle = angle % 180
            if normalized_angle > 90:
                normalized_angle -= 180
            
            # Check if line is horizontal (±15 degrees from 0)
            if abs(normalized_angle) <= 15:
                horizontal_count += 1
            # Check if line is vertical (±15 degrees from 90)
            elif abs(abs(normalized_angle) - 90) <= 15:
                vertical_count += 1
        
        # Determine if rotation is needed
        if vertical_count > horizontal_count * 1.5:
            # More vertical lines - likely rotated 90 degrees
            return True, 90
        elif horizontal_count > vertical_count * 1.5:
            # More horizontal lines - correct orientation
            return False, 0
        else:
            # Ambiguous - no clear orientation
            return False, 0
    
    def auto_rotate_image(self, image_bytes: bytes) -> Tuple[bytes, str]:
        """
        Auto-rotate image if needed
        Returns: (rotated_image_bytes, rotation_info)
        """
        needs_rotation, angle = self.detect_rotation(image_bytes)
        
        if not needs_rotation:
            return image_bytes, "No rotation needed"
        
        # Convert to cv2 image
        nparr = np.frombuffer(image_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Rotate image
        if angle == 90:
            rotated = cv2.rotate(img, cv2.ROTATE_90_COUNTERCLOCKWISE)
            info = "Rotated 90° counterclockwise"
        elif angle == -90:
            rotated = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
            info = "Rotated 90° clockwise"
        else:
            rotated = img
            info = "No rotation applied"
        
        # Convert back to bytes
        _, buffer = cv2.imencode('.jpg', rotated)
        return buffer.tobytes(), info

# Test function
def test_rotation_detection():
    import os
    detector = ChessScoreSheetRotationDetector()
    
    # Test with sample images
    test_images = [
        "d:/atliq_work/coach/image-to-text-main/data/1.jpeg",
        "d:/atliq_work/coach/image-to-text-main/data/3.jpeg"
    ]
    
    # Create output directory
    output_dir = "d:/atliq_work/coach/image-to-text-main/rotated_output"
    os.makedirs(output_dir, exist_ok=True)
    
    for img_path in test_images:
        try:
            with open(img_path, 'rb') as f:
                image_bytes = f.read()
            
            needs_rotation, angle = detector.detect_rotation(image_bytes)
            print(f"{img_path}: Needs rotation: {needs_rotation}, Angle: {angle}°")
            
            # Always save the result (rotated or original)
            rotated_bytes, info = detector.auto_rotate_image(image_bytes)
            print(f"  -> {info}")
            
            # Save rotated image
            filename = os.path.basename(img_path)
            name, ext = os.path.splitext(filename)
            output_path = os.path.join(output_dir, f"{name}_rotated{ext}")
            
            with open(output_path, 'wb') as f:
                f.write(rotated_bytes)
            print(f"  -> Saved: {output_path}")
                
        except FileNotFoundError:
            print(f"File not found: {img_path}")

if __name__ == "__main__":
    test_rotation_detection()