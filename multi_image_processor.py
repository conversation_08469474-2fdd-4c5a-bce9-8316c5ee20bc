"""
Multi-image chess scoresheet processor
Handles combining multiple pages of chess notation
"""

import re
import logging
from typing import List, Tuple, Optional

logger = logging.getLogger(__name__)

class MultiImageProcessor:
    
    def combine_pgn_pages(self, pgn_extractions: List[str]) -> Tuple[str, List[str]]:
        """
        Combine multiple PGN extractions from different pages
        
        Args:
            pgn_extractions: List of PGN strings from different images
            
        Returns:
            Tuple of (combined_pgn, warnings)
        """
        if not pgn_extractions:
            return "", ["No PGN extractions provided"]
        
        if len(pgn_extractions) == 1:
            return pgn_extractions[0], []
        
        warnings = []
        combined_moves = []
        combined_tags = {}
        
        # Process each PGN extraction
        page_info = []
        for i, pgn in enumerate(pgn_extractions):
            page_data = self._extract_page_info(pgn, i + 1)
            page_info.append(page_data)
            
            # Combine tags from first page
            if i == 0:
                combined_tags = page_data['tags']
        
        # Sort pages by starting move number
        page_info.sort(key=lambda x: x['start_move'])
        
        # Check for continuity and gaps
        expected_next_move = 1
        for page in page_info:
            if page['start_move'] > expected_next_move:
                gap_size = page['start_move'] - expected_next_move
                warnings.append(f"Gap detected: Missing moves {expected_next_move}-{page['start_move']-1}")
            elif page['start_move'] < expected_next_move:
                warnings.append(f"Overlap detected: Page starts at move {page['start_move']}")
            
            # Add moves from this page
            combined_moves.extend(page['moves'])
            expected_next_move = page['end_move'] + 1
        
        # Build combined PGN
        combined_pgn = self._build_combined_pgn(combined_tags, combined_moves)
        
        return combined_pgn, warnings
    
    def _extract_page_info(self, pgn: str, page_num: int) -> dict:
        """Extract move information from a single PGN page"""
        # Extract tags
        tags = {}
        tag_matches = re.findall(r'\[(\w+)\s+"([^"]+)"\]', pgn)
        for key, value in tag_matches:
            tags[key] = value
        
        # Extract moves
        # Remove tags and clean up
        moves_text = re.sub(r'\[.*?\]', '', pgn).strip()
        moves_text = moves_text.replace('0-1', '').replace('1-0', '').replace('1/2-1/2', '').strip()
        
        # Find all move pairs
        move_pairs = re.findall(r'(\d+)\.\s*([^\s]+)(?:\s+([^\s]+))?', moves_text)
        
        moves = []
        start_move = None
        end_move = None
        
        for move_num_str, white_move, black_move in move_pairs:
            move_num = int(move_num_str)
            
            if start_move is None:
                start_move = move_num
            end_move = move_num
            
            # Add moves in order
            if white_move and white_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                moves.append(f"{move_num}. {white_move}")
            if black_move and black_move not in ['1-0', '0-1', '1/2-1/2', '*']:
                moves.append(black_move)
        
        return {
            'page_num': page_num,
            'tags': tags,
            'moves': moves,
            'start_move': start_move or 1,
            'end_move': end_move or 1,
            'raw_pgn': pgn
        }
    
    def _build_combined_pgn(self, tags: dict, moves: List[str]) -> str:
        """Build a complete PGN from tags and moves"""
        pgn_lines = []
        
        # Add tags
        for key, value in tags.items():
            pgn_lines.append(f'[{key} "{value}"]')
        
        if pgn_lines:
            pgn_lines.append('')  # Empty line after tags
        
        # Add moves
        if moves:
            # Group moves by pairs
            move_line = ""
            current_move_num = 1
            move_count = 0
            
            for move in moves:
                if move.startswith(f"{current_move_num}."):
                    # This is a white move with number
                    if move_line:
                        pgn_lines.append(move_line.strip())
                    move_line = move + " "
                    move_count = 1
                else:
                    # This is a black move
                    move_line += move + " "
                    move_count += 1
                    
                    # After black move, increment move number
                    if move_count == 2:
                        current_move_num += 1
                        move_count = 0
            
            # Add final line if exists
            if move_line.strip():
                pgn_lines.append(move_line.strip())
        
        return '\n'.join(pgn_lines)
    
    def validate_multi_page_continuity(self, pgn_extractions: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate that multiple pages form a continuous game
        
        Returns:
            Tuple of (is_continuous, error_messages)
        """
        if len(pgn_extractions) <= 1:
            return True, []
        
        errors = []
        page_info = []
        
        # Extract info from each page
        for i, pgn in enumerate(pgn_extractions):
            page_data = self._extract_page_info(pgn, i + 1)
            page_info.append(page_data)
        
        # Sort by starting move
        page_info.sort(key=lambda x: x['start_move'])
        
        # Check continuity
        for i in range(len(page_info) - 1):
            current_page = page_info[i]
            next_page = page_info[i + 1]
            
            expected_next_start = current_page['end_move'] + 1
            actual_next_start = next_page['start_move']
            
            if actual_next_start != expected_next_start:
                if actual_next_start > expected_next_start:
                    errors.append(f"Gap between pages: Page {current_page['page_num']} ends at move {current_page['end_move']}, Page {next_page['page_num']} starts at move {actual_next_start}")
                else:
                    errors.append(f"Overlap between pages: Page {current_page['page_num']} ends at move {current_page['end_move']}, Page {next_page['page_num']} starts at move {actual_next_start}")
        
        return len(errors) == 0, errors