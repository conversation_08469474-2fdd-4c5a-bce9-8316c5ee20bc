#!/usr/bin/env python3
"""
Test script for smart validation functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chess_validator import ChessValidator

# Test PGN with a cascading error (wrong move at move 23)
test_pgn_with_error = """[Event "?"]
[Site "?"]
[Date "?"]
[Round "?"]
[White "Toby"]
[Black "Pudur R"]
[Result "*"]
[White<PERSON>lo "1142"]
[BlackElo "1500"]

1. e4 e5
2. Nf3 Nc6
3. Bc4 Nf6
4. d3 Bc5
5. c3 d6
6. O-O a6
7. a4 h6
8. h3 O-O
9. Re1 Be6
10. Bxe6 fxe6
11. d4 Ba7
12. dxe5 Nxe5
13. Nxe5 dxe5
14. Qb3 Qc8
15. Be3 Bxe3
16. Rxe3 Rd8
17. Na3 Rab8
18. Nc4 b5
19. Nxe5 bxa4
20. Qxa4 Rxb2
21. Nc6 Re8
22. e5 Nd5
23. Rd3 Qb7
24. Qxa6 Rb6
25. Qxb7 Rxb7
26. g3 Rb3
27. Ra7 Kh7
28. Nd4 Rb1
29. Kg2 c5
30. Nxe6 Rxe6
31. Rxd5 c4
32. f4 Rb3
33. f5 Reb6
34. f6 Rxc3
35. Rxg7+ Kh8
36. Rd8#"""

def test_smart_validation():
    print("Testing Smart Validation...")
    print("=" * 50)
    
    validator = ChessValidator()
    
    # Test smart validation
    print("Running smart validation...")
    is_valid, issues = validator.smart_validate_pgn(test_pgn_with_error)
    
    print(f"Valid: {is_valid}")
    print(f"Issues found: {len(issues)}")
    
    for i, issue in enumerate(issues):
        print(f"\nIssue {i+1}:")
        print(f"  Type: {issue.get('type', 'unknown')}")
        print(f"  Move: {issue.get('color', '')} {issue.get('move_number', '')} - {issue.get('invalid_move', '')}")
        print(f"  Error: {issue.get('error', '')}")
        
        if issue.get('type') == 'cascading_error':
            print(f"  Cascading count: {issue.get('cascading_count', 0)}")
            print(f"  Recovery: {issue.get('recovery_suggestion', '')}")
        
        if issue.get('suggestions'):
            print(f"  Suggestions: {len(issue['suggestions'])} available")
            for j, suggestion in enumerate(issue['suggestions'][:3]):
                if isinstance(suggestion, dict):
                    print(f"    {j+1}. {suggestion.get('first_move', '')} (score: {suggestion.get('score', 'N/A')})")
                else:
                    print(f"    {j+1}. {suggestion}")

if __name__ == "__main__":
    test_smart_validation()
