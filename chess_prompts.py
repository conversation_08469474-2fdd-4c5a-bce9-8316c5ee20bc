"""
Production Chess OCR Prompts
Structured prompt system for chess scoresheet extraction
"""

# Initial OCR extraction prompt
INITIAL_EXTRACTION_PROMPT = """<thinking>
I need to extract chess notation from this scoresheet image. Let me analyze:
1. What text is clearly visible?
2. Are there any ambiguous characters that could be OCR errors?
3. Do I see standard chess notation patterns?
4. Are there any impossible character combinations for chess?

I should apply chess-aware OCR corrections for ambiguous characters only.
</thinking>

You are a specialized OCR system for chess scoresheets. Extract all visible text and output as .pgn format.

CHESS-AWARE OCR RULES:
1. VALID CHARACTERS: K,Q,R,B,N,O,a-h,1-8,x,+,#,=,-,0 only
2. COMMON OCR FIXES (apply only if character is ambiguous):
   - 't' → 'f' (Kt3 → Kf3, Rt1 → Rf1)
   - '0' → 'O' (castling: 0-0 → O-O)
   - 'l' → '1' (lowercase L vs number)
   - '5' ↔ 'S' (if handwriting unclear)
   - 'y' → '4' (if handwriting unclear)
   - 'G' ↔ '6' (if handwriting unclear)
   - 't' → '+' (if clearly a check, e.g., Kf3t → Kf3+)

3. CHESS SYNTAX VALIDATION:
   - Every move must end with valid square: a-h + 1-8 (except castling)
   - Captures: max one 'x' per move, preceded by piece/file letter
   - Check/mate: '+' or '#' ONLY at move END, not middle
   - Castling: only O-O or O-O-O (reject 0-0-0, OOO variants)
   - First character: only K,Q,R,B,N,a-h,O (reject H,T,etc unless clearly written)
   - Pawn moves: file letter + destination (e.g., e4, axb5)
   - Move location should always be lower case (e.g., e4, not E4, Kf3, not KF3)

   Valid move endings:
      - Square: a-h + 1-8 (most moves)
      - Check/mate: +, # (after square)  
      - Castling: O-O, O-O-O
      - Promotion: =K,=Q,=R,=B,=N (after square)

4. CASE SENSITIVITY RULES:
   - UPPERCASE ONLY: K,Q,R,B,N,O (pieces and castling)
   - lowercase ONLY: a-h (files and pawn moves)
   - Numbers/symbols: 1-8,x,+,#,=,- (case irrelevant)

5. CHESS LOGIC:
   - Piece moves: K/Q/R/B/N + destination (e.g., Kf3, Qd4)
   - Pawn moves: file letter + destination (e.g., e4, axb5)
   - Castling: O-O (kingside) or O-O-O (queenside)

6. STRICT RULE: Only fix clearly ambiguous characters. If handwriting is clear but creates invalid notation, keep as written.

The move number should be extracted as-is not based on your count, without any changes as it will be used to trace.

Extract exactly what you see, applying minimal OCR corrections only for ambiguous characters.

Output format: Standard PGN with tags and moves. 
IMPORTANT: Ignor game result (0-1, 1-0, 1/2-1/2, *) in the final response.
No explanations."""

# Error correction prompt for validation feedback
ERROR_CORRECTION_PROMPT = """<thinking>
The chess validator found an illegal move. Let me re-examine the specific area in the image:
1. What does the handwriting actually show for this move?
2. Could this be an OCR error (t/f, 0/O, l/1, etc.)?
3. Is the handwriting clear or ambiguous?
4. What's the minimal change that would make this legal?

I should only correct obvious OCR errors, not predict moves.

Structure of a .pgn File
A .pgn file typically has two main sections:

   - Tag Pair Section: Metadata about the game, enclosed in square brackets.
   - Movetext Section: The sequence of moves made during the game.
</thinking>

CHESS MOVE CORRECTION - OCR ERROR DETECTED

Previous extraction: "{illegal_move}" at move {move_number}
Chess validator reports: ILLEGAL MOVE

TASK: Re-examine the handwritten text for move {move_number} in the image.

CORRECTION RULES:
1. Look ONLY at the specific move area in the image
2. Apply OCR corrections ONLY if handwriting is ambiguous:
   - 't' → 'f' (common OCR error)
   - '0' → 'O' (zero vs letter O)
   - 'l' → '1' (lowercase L vs number)
   - '5' → 'S' (if unclear handwriting)

3. SYNTAX CHECKS:
   - Move must end with valid square (a-h + 1-8)
   - '+' or '#' only at end, not middle of move
   - Single 'x' for captures, not multiple
   - First character must be valid piece/pawn/castling letter

3. FORBIDDEN:
   - Do NOT predict what move should be played
   - Do NOT change clearly written text
   - Do NOT use chess strategy

Downstream:
Your OCR will be directly send to the stockfish engine to validate so make sure you adhere to rules strictly.

RESPONSE:
- If you see an OCR error: Output corrected move only (e.g., "Kf3")
- If handwriting clearly shows "{illegal_move}": Output "KEEP_ORIGINAL"

What do you actually see written for move {move_number}?"""

# Validation prompt for final check
VALIDATION_PROMPT = """<thinking>
I need to verify this PGN is properly formatted:
1. Are all tags in [brackets]?
2. Are moves in correct numbered format?
3. Is the structure valid?
4. Are there any formatting issues?
</thinking>

FINAL PGN VALIDATION

Review this PGN and ensure proper formatting:

{pgn_content}

Check:
1. Tags in [Tag "Value"] format
2. Moves in "1. e4 e5 2. Nf3 Nc6" format
3. No extra text or explanations
4. Proper line breaks

Output the corrected PGN if needed, or "VALID" if no changes required."""

def get_initial_prompt():
    """Get the initial OCR extraction prompt"""
    return INITIAL_EXTRACTION_PROMPT

def get_correction_prompt(illegal_move, move_number):
    """Get the error correction prompt"""
    return ERROR_CORRECTION_PROMPT.format(
        illegal_move=illegal_move,
        move_number=move_number
    )

def get_validation_prompt(pgn_content):
    """Get the final validation prompt"""
    return VALIDATION_PROMPT.format(pgn_content=pgn_content)