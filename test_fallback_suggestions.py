#!/usr/bin/env python3
"""
Test script for fallback suggestions
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from chess_validator import ChessValidator

def test_fallback_suggestions():
    print("Testing Fallback Suggestions...")
    print("=" * 50)
    
    validator = ChessValidator()
    
    # Test common OCR errors
    test_moves = ['Rb0', 'Qb0', 'Kb0', 'Nb0', 'R0', 'Kl', 'QO', 'B8']
    
    for move in test_moves:
        print(f"\nTesting move: {move}")
        suggestions = validator.get_fallback_suggestions(move)
        print(f"Suggestions ({len(suggestions)}):")
        for i, suggestion in enumerate(suggestions):
            print(f"  {i+1}. {suggestion['first_move']} (score: {suggestion['score']})")

if __name__ == "__main__":
    test_fallback_suggestions()
