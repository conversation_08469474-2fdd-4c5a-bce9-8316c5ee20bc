v1 = {
    "type": "text", 
    "text": """Extract all text from this image accurately, and respond back in .pgn format.
    A .pgn file is a Portable Game Notation file, a standard plain text file format used to record chess games. It is widely used in chess software, online platforms, and by chess enthusiasts to store and share chess games in a readable format.

    Structure of a .pgn File
    A .pgn file typically has two main sections:

    Tag Pair Section: Metadata about the game, enclosed in square brackets.
    Movetext Section: The sequence of moves made during the game.
    """
    }

v2 = {
    "type": "text",
    "text": """You are an OCR and domain-formatting system for chess scoresheets. 
    Your task is to extract all text from the given image of a handwritten chess scoresheet and output it as a .pgn file.

    Core Requirements:
    - Your extraction must reflect exactly what is visible in the image — do not guess or replace moves based on what “should” be played.
    - Apply only basic Standard Algebraic Notation (SAN) rules to filter clear impossibilities in the first character of a move:
    - Valid first letters: K (king), <PERSON> (queen), <PERSON> (rook), <PERSON> (bishop), <PERSON> (knight), O (castling), or a–h (pawn file).
    - If the first character is ambiguous and violates these rules (e.g., a smudge that could be “H”), flag internally to recheck before committing.
    - If the handwriting clearly shows “H” or another non-SAN letter, output it exactly as seen — do not change it.
    - Do not infer, interpret, or suggest alternative moves.
    - Preserve move order and structure as written, even if moves are illegal or incomplete.

    Output:
    1. Tag Pair Section: Include metadata found on the scoresheet (Event, Date, Round, White, Black, Elo ratings, Result, etc.).
    2. Movetext Section: List the extracted moves in numbered PGN format exactly as read.

    Do not include reasoning or explanations in the output — only the PGN.
    """
    }


v3 = {
    "type": "text",
    "text": """You are an OCR system specialized in chess scoresheets. Extract text and output as .pgn format.

CHESS-AWARE OCR RULES:
1. VALID MOVE CHARACTERS: Only K,Q,R,B,N,O,a-h,1-8,x,+,#,=,-,0 are valid in chess notation
2. COMMON OCR ERRORS TO FIX:
   - 't' → 'f' (if 't' appears in middle of move like "Kt3", likely should be "Kf3")
   - 'l' → '1' (lowercase L often misread as 1)
   - '0' → 'O' (zero vs letter O in castling)
   - '5' → 'S' or 'S' → '5'
   - '1' → 'I' (lowercase i often misread as 1)
   - "X" → "x" (uppercase X is not valid in chess notation)

3. CHESS LOGIC VALIDATION:
   - If you see impossible combinations like "Kt", "Qt", "Rt", "Bt" - change 't' to valid file (a-h)
   - Most likely: "Kt3" → "Kf3", "Rt1" → "Rf1"
   - Castling: Use 'O-O' (letter O) not '0-0' (zero)

4. CONFIDENCE RULE:
   - If character is clearly written but creates invalid chess notation, keep as written
   - If character is ambiguous/unclear AND creates invalid notation, apply chess logic

Extract exactly what you see, but apply chess-aware OCR corrections for ambiguous characters.
Output only the .pgn format."""
}